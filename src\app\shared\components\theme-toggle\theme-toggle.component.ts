import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { AsyncPipe } from '@angular/common';
import { ThemeService } from '../../../core/services/theme.service';
import { ThemeMode } from '../../../core/store/states/theme.state';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, AsyncPipe],
  template: `
    <button
      mat-icon-button
      class="text-center rounded-full w-10 h-10"
      style="background-color: var(--md-sys-color-surface-container-highest)"
      (click)="toggleTheme()">
      @if (currentTheme$ | async; as theme) {
        @if (theme === 'light') {
          <mat-icon style="color: var(--md-sys-color-primary)">dark_mode</mat-icon>
        } @else {
          <mat-icon style="color: var(--md-sys-color-primary)">light_mode</mat-icon>
        }
      }
    </button>
  `,
  styleUrls: ['./theme-toggle.component.scss'],
})
export class ThemeToggleComponent {
  currentTheme$: Observable<ThemeMode>;

  constructor(private themeService: ThemeService) {
    this.currentTheme$ = this.themeService.currentTheme$;
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }
}
