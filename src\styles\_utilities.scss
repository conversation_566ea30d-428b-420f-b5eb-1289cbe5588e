/* ========================================
   THEME-AWARE UTILITY CLASSES
   ======================================== */

/* 
 * These utilities follow Material Design 3 naming conventions
 * and provide theme-aware styling for common use cases
 */

// Background utilities
.u-bg-surface { 
  background-color: var(--md-sys-color-surface); 
}

.u-bg-surface-container { 
  background-color: var(--md-sys-color-surface-container); 
}

.u-bg-surface-container-low { 
  background-color: var(--md-sys-color-surface-container-low); 
}

.u-bg-surface-container-high { 
  background-color: var(--md-sys-color-surface-container-high); 
}

.u-bg-surface-container-highest { 
  background-color: var(--md-sys-color-surface-container-highest); 
}

.u-bg-primary { 
  background-color: var(--md-sys-color-primary); 
}

.u-bg-primary-container { 
  background-color: var(--md-sys-color-primary-container); 
}

.u-bg-surface-variant { 
  background-color: var(--md-sys-color-surface-variant); 
}

// Text color utilities
.u-text-on-surface { 
  color: var(--md-sys-color-on-surface); 
}

.u-text-on-surface-variant { 
  color: var(--md-sys-color-on-surface-variant); 
}

.u-text-on-primary { 
  color: var(--md-sys-color-on-primary); 
}

.u-text-on-primary-container { 
  color: var(--md-sys-color-on-primary-container); 
}

.u-text-primary { 
  color: var(--md-sys-color-primary); 
}

.u-text-on-background { 
  color: var(--md-sys-color-on-background); 
}

// Border utilities
.u-border-outline { 
  border-color: var(--md-sys-color-outline); 
}

.u-border-outline-variant { 
  border-color: var(--md-sys-color-outline-variant); 
}

// Icon color utilities
.u-icon-primary {
  color: var(--md-sys-color-primary);
}

.u-icon-on-surface {
  color: var(--md-sys-color-on-surface);
}

.u-icon-on-surface-variant {
  color: var(--md-sys-color-on-surface-variant);
}

.u-icon-on-primary {
  color: var(--md-sys-color-on-primary);
}

/* ========================================
   HEADER ICON BUTTON UTILITY
   ======================================== */

/* Base header icon button styling */
.header-icon-button {
  color: var(--md-sys-color-primary);

  &:hover {
    background-color: var(--md-sys-color-surface-container-high);
  }
}

/* Light mode specific styling */
.light-mode .header-icon-button {
  background-color: #ffffff;
}

/* Dark mode specific styling */
.dark-mode .header-icon-button {
  background-color: #36393e;
}
