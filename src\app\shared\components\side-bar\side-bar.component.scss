@use '@angular/material' as mat;

.mat-drawer-container {
  box-shadow: 19px 0px 50px -3px rgba(0, 0, 0, 0.1);
  width: 80px;
}

.hamburger-btn {
  position: absolute;
  left: 90px;
}

// Style the width using a custom class and ::ng-deep
// to affect the globally-placed tooltip panel.
::ng-deep .sidebar-tooltip {
  max-width: 100px;
  text-align: center;
  white-space: pre-line;
}

::ng-deep .mdc-tooltip__surface {
  background-color: #f7f9ff !important;
  color: black !important;
}
