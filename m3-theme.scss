@use 'sass:map';
@use '@angular/material' as mat;

$_palettes: (
  primary: (
    0: #000000,
    10: #001d36,
    20: #003258,
    25: #003d6b,
    30: #00497d,
    35: #18558a,
    40: #296197,
    50: #467ab2,
    60: #6194cd,
    70: #7cafea,
    80: #9fcaff,
    90: #d1e4ff,
    95: #eaf1ff,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #001f24,
    20: #00363d,
    25: #00424a,
    30: #004f58,
    35: #005b66,
    40: #016874,
    50: #2f828e,
    60: #4d9ca8,
    70: #6ab7c4,
    80: #85d2e0,
    90: #a2effd,
    95: #d1f8ff,
    98: #edfcff,
    99: #f6feff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #220f46,
    20: #38265d,
    25: #433169,
    30: #4f3d75,
    35: #5b4881,
    40: #67548e,
    50: #816da9,
    60: #9b86c4,
    70: #b6a1e1,
    80: #d2bcfe,
    90: #eaddff,
    95: #f6edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    4: #0a0b0d,
    6: #0f1113,
    10: #191c20,
    12: #1d2024,
    17: #282b2f,
    20: #2e3135,
    22: #323539,
    24: #37393e,
    25: #393b40,
    30: #44474c,
    35: #505257,
    40: #5c5e63,
    50: #75777c,
    60: #8f9196,
    70: #a9abb1,
    80: #c5c6cc,
    87: #d9dae0,
    90: #e1e2e8,
    92: #e7e8ee,
    94: #ecedf3,
    95: #eff0f6,
    96: #f2f3f9,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  neutral-variant: (
    0: #000000,
    10: #161c23,
    20: #2b3139,
    25: #363c44,
    30: #42474f,
    35: #4d535b,
    40: #595f67,
    50: #727780,
    60: #8c919a,
    70: #a6abb5,
    80: #c2c7d1,
    90: #dee3ed,
    95: #ecf1fb,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_secondary: map.merge(map.get($_palettes, secondary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);
