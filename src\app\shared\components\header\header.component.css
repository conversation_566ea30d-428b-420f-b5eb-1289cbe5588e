/* ========================================
   HEADER COMPONENT STYLES
   ======================================== */

/* Menu styling */
::ng-deep .mat-mdc-menu-content {
  padding: 0 !important;
}

::ng-deep .mat-mdc-menu-panel {
  max-width: initial !important;
  overflow: hidden !important;
}

/* Header button styling */
.header-icon-button {
  color: var(--md-sys-color-primary);

  &:hover {
    background-color: var(--md-sys-color-surface-container-high);
  }
}

/* Light mode specific styling */
.light-mode .header-icon-button {
  background-color: #ffffff;
}

/* Dark mode specific styling */
.dark-mode .header-icon-button {
  background-color: var(--md-sys-color-surface-container-highest);
}

/* Notification card styling */
.notification-card {
  background-color: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
}
