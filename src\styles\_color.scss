.light-mode {
  --md-sys-color-primary: #296197;
  --md-sys-color-surface-tint: #296197;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #d2e4ff;
  --md-sys-color-on-primary-container: #001d36;
  --md-sys-color-secondary: #006874;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #9eeffd;
  --md-sys-color-on-secondary-container: #001f24;
  --md-sys-color-tertiary: #67548e;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #eaddff;
  --md-sys-color-on-tertiary-container: #220f46;
  --md-sys-color-error: #8f4a4e;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffdada;
  --md-sys-color-on-error-container: #3b080f;
  --md-sys-color-surface-light: #fafbff;
  --md-sys-color-background: #f8f9ff;
  --md-sys-color-on-background: #191c20;
  --md-sys-color-surface: #f7f9ff;
  --md-sys-color-on-surface: #181c20;
  --md-sys-color-surface-variant: #dfe2eb;
  --md-sys-color-on-surface-variant: #43474e;
  --md-sys-color-outline: #73777f;
  --md-sys-color-outline-variant: #c3c6cf;
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
  --md-sys-color-inverse-surface: #2d3135;
  --md-sys-color-inverse-on-surface: #eff1f6;
  --md-sys-color-inverse-primary: #a1cafd;
  --md-sys-color-primary-fixed: #d2e4ff;
  --md-sys-color-on-primary-fixed: #001d36;
  --md-sys-color-primary-fixed-dim: #a1cafd;
  --md-sys-color-on-primary-fixed-variant: #1a4975;
  --md-sys-color-secondary-fixed: #9eeffd;
  --md-sys-color-on-secondary-fixed: #001f24;
  --md-sys-color-secondary-fixed-dim: #82d3e0;
  --md-sys-color-on-secondary-fixed-variant: #004f58;
  --md-sys-color-tertiary-fixed: #eaddff;
  --md-sys-color-on-tertiary-fixed: #220f46;
  --md-sys-color-tertiary-fixed-dim: #d2bcfd;
  --md-sys-color-on-tertiary-fixed-variant: #4f3d74;
  --md-sys-color-surface-dim: #d8dae0;
  --md-sys-color-surface-bright: #f7f9ff;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f1f3f9;
  --md-sys-color-surface-container: #eceef4;
  --md-sys-color-surface-container-high: #e6e8ee;
  --md-sys-color-surface-container-highest: #e0e2e8;
}

/* AICU-M3.light-high-contrast.tokens.scss */

.light-high-contrast {
  --md-sys-color-primary: #002341;
  --md-sys-color-surface-tint: #296197;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #154571;
  --md-sys-color-on-primary-container: #ffffff;
  --md-sys-color-secondary: #00272c;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #004a53;
  --md-sys-color-on-secondary-container: #ffffff;
  --md-sys-color-tertiary: #29174d;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #4b3970;
  --md-sys-color-on-tertiary-container: #ffffff;
  --md-sys-color-error: #440f15;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #6e2f33;
  --md-sys-color-on-error-container: #ffffff;
  --md-sys-color-surface-light: #fafbff;
  --md-sys-color-background: #f8f9ff;
  --md-sys-color-on-background: #191c20;
  --md-sys-color-surface: #f7f9ff;
  --md-sys-color-on-surface: #000000;
  --md-sys-color-surface-variant: #dfe2eb;
  --md-sys-color-on-surface-variant: #20242b;
  --md-sys-color-outline: #3f434a;
  --md-sys-color-outline-variant: #3f434a;
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
  --md-sys-color-inverse-surface: #2d3135;
  --md-sys-color-inverse-on-surface: #ffffff;
  --md-sys-color-inverse-primary: #e2edff;
  --md-sys-color-primary-fixed: #154571;
  --md-sys-color-on-primary-fixed: #ffffff;
  --md-sys-color-primary-fixed-dim: #002e53;
  --md-sys-color-on-primary-fixed-variant: #ffffff;
  --md-sys-color-secondary-fixed: #004a53;
  --md-sys-color-on-secondary-fixed: #ffffff;
  --md-sys-color-secondary-fixed-dim: #003238;
  --md-sys-color-on-secondary-fixed-variant: #ffffff;
  --md-sys-color-tertiary-fixed: #4b3970;
  --md-sys-color-on-tertiary-fixed: #ffffff;
  --md-sys-color-tertiary-fixed-dim: #342258;
  --md-sys-color-on-tertiary-fixed-variant: #ffffff;
  --md-sys-color-surface-dim: #d8dae0;
  --md-sys-color-surface-bright: #f7f9ff;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f1f3f9;
  --md-sys-color-surface-container: #eceef4;
  --md-sys-color-surface-container-high: #e6e8ee;
  --md-sys-color-surface-container-highest: #e0e2e8;
}

/* AICU-M3.light-medium-contrast.tokens.scss */

.light-medium-contrast {
  --md-sys-color-primary: #154571;
  --md-sys-color-surface-tint: #296197;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #4e77a6;
  --md-sys-color-on-primary-container: #ffffff;
  --md-sys-color-secondary: #004a53;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #25808c;
  --md-sys-color-on-secondary-container: #ffffff;
  --md-sys-color-tertiary: #4b3970;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #7e6ba6;
  --md-sys-color-on-tertiary-container: #ffffff;
  --md-sys-color-error: #6e2f33;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #aa5f63;
  --md-sys-color-on-error-container: #ffffff;
  --md-sys-color-surface-light: #fafbff;
  --md-sys-color-background: #f8f9ff;
  --md-sys-color-on-background: #191c20;
  --md-sys-color-surface: #f7f9ff;
  --md-sys-color-on-surface: #181c20;
  --md-sys-color-surface-variant: #dfe2eb;
  --md-sys-color-on-surface-variant: #3f434a;
  --md-sys-color-outline: #5b5f67;
  --md-sys-color-outline-variant: #777b83;
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
  --md-sys-color-inverse-surface: #2d3135;
  --md-sys-color-inverse-on-surface: #eff1f6;
  --md-sys-color-inverse-primary: #a1cafd;
  --md-sys-color-primary-fixed: #4e77a6;
  --md-sys-color-on-primary-fixed: #ffffff;
  --md-sys-color-primary-fixed-dim: #345e8c;
  --md-sys-color-on-primary-fixed-variant: #ffffff;
  --md-sys-color-secondary-fixed: #25808c;
  --md-sys-color-on-secondary-fixed: #ffffff;
  --md-sys-color-secondary-fixed-dim: #006671;
  --md-sys-color-on-secondary-fixed-variant: #ffffff;
  --md-sys-color-tertiary-fixed: #7e6ba6;
  --md-sys-color-on-tertiary-fixed: #ffffff;
  --md-sys-color-tertiary-fixed-dim: #65528b;
  --md-sys-color-on-tertiary-fixed-variant: #ffffff;
  --md-sys-color-surface-dim: #d8dae0;
  --md-sys-color-surface-bright: #f7f9ff;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f1f3f9;
  --md-sys-color-surface-container: #eceef4;
  --md-sys-color-surface-container-high: #e6e8ee;
  --md-sys-color-surface-container-highest: #e0e2e8;
}

/* AICU-M3.dark.tokens.scss */

.dark-mode {
  --md-sys-color-primary: #a1cafd;
  --md-sys-color-surface-tint: #a1cafd;
  --md-sys-color-on-primary: #003259;
  --md-sys-color-primary-container: #1a4975;
  --md-sys-color-on-primary-container: #d2e4ff;
  --md-sys-color-secondary: #82d3e0;
  --md-sys-color-on-secondary: #00363d;
  --md-sys-color-secondary-container: #004f58;
  --md-sys-color-on-secondary-container: #9eeffd;
  --md-sys-color-tertiary: #d2bcfd;
  --md-sys-color-on-tertiary: #38265c;
  --md-sys-color-tertiary-container: #4f3d74;
  --md-sys-color-on-tertiary-container: #eaddff;
  --md-sys-color-error: #ffb3b5;
  --md-sys-color-on-error: #561d22;
  --md-sys-color-error-container: #733337;
  --md-sys-color-on-error-container: #ffdada;
  --md-sys-color-surface-light: #fffffa;
  --md-sys-color-background: #111418;
  --md-sys-color-on-background: #e1e2e8;
  --md-sys-color-surface: #101418;
  --md-sys-color-on-surface: #e0e2e8;
  --md-sys-color-surface-variant: #43474e;
  --md-sys-color-on-surface-variant: #c3c6cf;
  --md-sys-color-outline: #8d9199;
  --md-sys-color-outline-variant: #43474e;
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
  --md-sys-color-inverse-surface: #e0e2e8;
  --md-sys-color-inverse-on-surface: #2d3135;
  --md-sys-color-inverse-primary: #36618e;
  --md-sys-color-primary-fixed: #d2e4ff;
  --md-sys-color-on-primary-fixed: #001d36;
  --md-sys-color-primary-fixed-dim: #a1cafd;
  --md-sys-color-on-primary-fixed-variant: #1a4975;
  --md-sys-color-secondary-fixed: #9eeffd;
  --md-sys-color-on-secondary-fixed: #001f24;
  --md-sys-color-secondary-fixed-dim: #82d3e0;
  --md-sys-color-on-secondary-fixed-variant: #004f58;
  --md-sys-color-tertiary-fixed: #eaddff;
  --md-sys-color-on-tertiary-fixed: #220f46;
  --md-sys-color-tertiary-fixed-dim: #d2bcfd;
  --md-sys-color-on-tertiary-fixed-variant: #4f3d74;
  --md-sys-color-surface-dim: #101418;
  --md-sys-color-surface-bright: #36393e;
  --md-sys-color-surface-container-lowest: #0b0e12;
  --md-sys-color-surface-container-low: #181c20;
  --md-sys-color-surface-container: #1c2024;
  --md-sys-color-surface-container-high: #272a2f;
  --md-sys-color-surface-container-highest: #323539;
}

/* ========================================
   UTILITY CLASSES FOR THEME COLORS
   ======================================== */

// Background Colors
.bg-surface { background-color: var(--md-sys-color-surface); }
.bg-surface-container { background-color: var(--md-sys-color-surface-container); }
.bg-surface-container-low { background-color: var(--md-sys-color-surface-container-low); }
.bg-surface-container-high { background-color: var(--md-sys-color-surface-container-high); }
.bg-surface-container-highest { background-color: var(--md-sys-color-surface-container-highest); }
.bg-primary { background-color: var(--md-sys-color-primary); }
.bg-primary-container { background-color: var(--md-sys-color-primary-container); }
.bg-surface-variant { background-color: var(--md-sys-color-surface-variant); }

// Text Colors
.text-on-surface { color: var(--md-sys-color-on-surface); }
.text-on-surface-variant { color: var(--md-sys-color-on-surface-variant); }
.text-on-primary { color: var(--md-sys-color-on-primary); }
.text-on-primary-container { color: var(--md-sys-color-on-primary-container); }
.text-primary { color: var(--md-sys-color-primary); }
.text-on-background { color: var(--md-sys-color-on-background); }

// Border Colors
.border-outline { border-color: var(--md-sys-color-outline); }
.border-outline-variant { border-color: var(--md-sys-color-outline-variant); }

// Icon Colors
.icon-primary { color: var(--md-sys-color-primary); }
.icon-on-surface { color: var(--md-sys-color-on-surface); }
.icon-on-surface-variant { color: var(--md-sys-color-on-surface-variant); }
.icon-on-primary { color: var(--md-sys-color-on-primary); }

// Component-specific classes
.theme-button {
  background-color: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline-variant);

  &:hover {
    background-color: var(--md-sys-color-surface-container-high);
  }
}

.theme-card {
  background-color: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.theme-sidebar {
  background-color: var(--md-sys-color-surface-container);

  .divider {
    border-color: var(--md-sys-color-outline-variant);
  }

  .icon {
    color: var(--md-sys-color-on-surface-variant);

    &.active {
      color: var(--md-sys-color-on-primary);
    }
  }
}

/* fonts.tokens.scss */

:root {
  --md-sys-typescale-display-large-font: Roboto;
  --md-sys-typescale-display-large-size: 57px;
  --md-sys-typescale-display-large-tracking: -0.25px;
  --md-sys-typescale-display-large-weight: Regular;
  --md-sys-typescale-display-medium-font: Roboto;
  --md-sys-typescale-display-medium-size: 45px;
  --md-sys-typescale-display-medium-tracking: 0px;
  --md-sys-typescale-display-medium-weight: Regular;
  --md-sys-typescale-display-small-font: Roboto;
  --md-sys-typescale-display-small-size: 36px;
  --md-sys-typescale-display-small-tracking: 0px;
  --md-sys-typescale-display-small-weight: Regular;
  --md-sys-typescale-headline-large-font: Roboto;
  --md-sys-typescale-headline-large-size: 32px;
  --md-sys-typescale-headline-large-tracking: 0px;
  --md-sys-typescale-headline-large-weight: 500;
  --md-sys-typescale-headline-medium-font: Roboto;
  --md-sys-typescale-headline-medium-size: 28px;
  --md-sys-typescale-headline-medium-tracking: 0px;
  --md-sys-typescale-headline-medium-weight: 500;
  --md-sys-typescale-headline-small-font: Roboto;
  --md-sys-typescale-headline-small-size: 22px;
  --md-sys-typescale-headline-small-tracking: 0px;
  --md-sys-typescale-headline-small-weight: 400;
  --md-sys-typescale-title-large-font: Roboto;
  --md-sys-typescale-title-large-size: 22px;
  --md-sys-typescale-title-large-tracking: 0px;
  --md-sys-typescale-title-large-weight: Regular;
  --md-sys-typescale-title-medium-font: Roboto;
  --md-sys-typescale-title-medium-size: 16px;
  --md-sys-typescale-title-medium-tracking: 0.15px;
  --md-sys-typescale-title-medium-weight: 500;
  --md-sys-typescale-title-small-font: Roboto;
  --md-sys-typescale-title-small-size: 14px;
  --md-sys-typescale-title-small-tracking: 0.1px;
  --md-sys-typescale-title-small-weight: 400;
  --md-sys-typescale-body-large-font: Roboto;
  --md-sys-typescale-body-large-size: 16px;
  --md-sys-typescale-body-large-tracking: 0.5px;
  --md-sys-typescale-body-large-weight: Regular;
  --md-sys-typescale-body-medium-font: Roboto;
  --md-sys-typescale-body-medium-size: 14px;
  --md-sys-typescale-body-medium-tracking: 0.25px;
  --md-sys-typescale-body-medium-weight: Regular;
  --md-sys-typescale-body-small-font: Roboto;
  --md-sys-typescale-body-small-size: 12px;
  --md-sys-typescale-body-small-tracking: 0.4px;
  --md-sys-typescale-body-small-weight: Regular;
  --md-sys-typescale-label-large-prominent-font: Roboto;
  --md-sys-typescale-label-large-prominent-size: 14px;
  --md-sys-typescale-label-large-prominent-tracking: 0.1px;
  --md-sys-typescale-label-large-prominent-weight: 400;
  --md-sys-typescale-label-large-font: Roboto;
  --md-sys-typescale-label-large-size: 14px;
  --md-sys-typescale-label-large-tracking: 0.1px;
  --md-sys-typescale-label-large-weight: 500;
  --md-sys-typescale-label-medium-prominent-font: Roboto;
  --md-sys-typescale-label-medium-prominent-size: 12px;
  --md-sys-typescale-label-medium-prominent-tracking: 0.5px;
  --md-sys-typescale-label-medium-prominent-weight: 400;
  --md-sys-typescale-label-medium-font: Roboto;
  --md-sys-typescale-label-medium-size: 12px;
  --md-sys-typescale-label-medium-tracking: 0.5px;
  --md-sys-typescale-label-medium-weight: 500;
  --md-sys-typescale-label-small-font: Roboto;
  --md-sys-typescale-label-small-size: 11px;
  --md-sys-typescale-label-small-tracking: 0.5px;
  --md-sys-typescale-label-small-weight: 500;
}
